import requests
import json
from .日志 import logger, send_remote_log, get_current_user_code
from .配置 import DATA_URL
from .VDF处理 import 处理解压目录中的VDF文件
import tempfile
import os
import urllib.request
import urllib.parse
import shutil
import logging
import subprocess

# 在导入patoolib之前配置日志
import sys
import io

# 临时重定向stderr来阻止patoolib的输出
original_stderr = sys.stderr
sys.stderr = io.StringIO()

try:
    import patoolib
finally:
    sys.stderr = original_stderr

# 禁用patoolib的日志输出
patool_logger = logging.getLogger('patoolib')
patool_logger.setLevel(logging.CRITICAL)
patool_logger.disabled = True
patool_logger.propagate = False

# 禁用所有patoolib相关的日志
for handler in patool_logger.handlers[:]:
    patool_logger.removeHandler(handler)

def get_game_list():
    """从API获取游戏列表数据"""
    try:
        response = requests.get(DATA_URL, timeout=60)
        response.raise_for_status()
        data = response.json()
        return data
    except requests.RequestException as e:
        return []
    except json.JSONDecodeError as e:
        return []

def get_game_details(app_id):
    """从Steam API获取游戏详情"""
    try:
        url = f"https://store.steampowered.com/api/appdetails?appids={app_id}&cc=cn&l=schinese"
        response = requests.get(url, timeout=60)
        response.raise_for_status()
        data = response.json()
        return data
    except requests.RequestException as e:
        return {"error": str(e)}
    except json.JSONDecodeError as e:
        return {"error": f"JSON解析失败: {str(e)}"}

def 获取游戏信息(app_id):
    """
    从Steam API获取游戏详情信息
    """
    url = f"https://store.steampowered.com/api/appdetails?appids={app_id}"
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            return {"status": "error", "message": f"API请求失败: {response.status_code}"}
    except Exception as e:
        return {"status": "error", "message": f"请求异常: {str(e)}"}

def 检测Steam网络连接():
    """
    检测Steam网络连接状态
    通过访问Steam商店主页来判断网络连接是否正常
    """
    try:
        # 测试Steam商店主页
        url = "https://store.steampowered.com/"

        # 设置请求头，模拟浏览器访问
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        # 发送请求，设置较短的超时时间
        response = requests.get(url, headers=headers, timeout=10, allow_redirects=True)

        # 检查响应状态
        if response.status_code == 200:
            # 检查响应内容是否包含Steam特征内容
            content = response.text.lower()
            if 'steam' in content and ('store' in content or 'valve' in content):
                return {
                    "status": "success",
                    "connected": True,
                    "response_time": response.elapsed.total_seconds(),
                    "status_code": response.status_code,
                    "message": "Steam网络连接正常"
                }
            else:
                return {
                    "status": "warning",
                    "connected": False,
                    "response_time": response.elapsed.total_seconds(),
                    "status_code": response.status_code,
                    "message": "访问成功但内容异常"
                }
        else:
            return {
                "status": "error",
                "connected": False,
                "response_time": response.elapsed.total_seconds() if hasattr(response, 'elapsed') else 0,
                "status_code": response.status_code,
                "message": f"HTTP状态码: {response.status_code}"
            }

    except requests.exceptions.Timeout:
        return {
            "status": "error",
            "connected": False,
            "response_time": 10.0,
            "status_code": 0,
            "message": "连接超时"
        }
    except requests.exceptions.ConnectionError:
        return {
            "status": "error",
            "connected": False,
            "response_time": 0,
            "status_code": 0,
            "message": "网络连接失败"
        }
    except requests.exceptions.RequestException as e:
        return {
            "status": "error",
            "connected": False,
            "response_time": 0,
            "status_code": 0,
            "message": f"请求异常: {str(e)}"
        }
    except Exception as e:
        return {
            "status": "error",
            "connected": False,
            "response_time": 0,
            "status_code": 0,
            "message": f"未知错误: {str(e)}"
        }

def 一键入库(游戏名称, 游戏类型, app_id):
    """
    发送一键入库请求并处理下载和解压
    """
    user_code = get_current_user_code()

    # 构造请求数据
    文件名 = f"{游戏名称}----{游戏类型}----{app_id}"
    下载链接 = None

    send_remote_log('info', '请求游戏资源下载链接', {
        'game_name': 游戏名称,
        'game_type': 游戏类型,
        'app_id': app_id,
        'function': '一键入库'
    }, user_code)

    try:
        # 发送POST请求
        response = requests.post(
            "http://**************:9697/download.php",
            data={"data": 文件名},
            timeout=15
        )

        # 检查响应是否成功
        if response.status_code == 200 and "File not found" not in response.text:
            下载链接 = response.text.strip()
            send_remote_log('info', '成功获取游戏资源下载链接', {
                'game_name': 游戏名称,
                'app_id': app_id,
                'has_download_url': bool(下载链接)
            }, user_code)
            return {"status": "success", "下载链接": 下载链接}
        else:
            send_remote_log('warning', '未找到游戏资源', {
                'game_name': 游戏名称,
                'app_id': app_id,
                'response_status': response.status_code
            }, user_code)
            return {"status": "error", "message": "未找到该游戏资源"}
    except Exception as e:
        send_remote_log('error', '游戏资源请求异常', {
            'game_name': 游戏名称,
            'app_id': app_id,
            'error': str(e)
        }, user_code)
        return {"status": "error", "message": f"入库请求失败: {str(e)}"}

def 下载并解压文件(下载链接, 文件名, app_id=None):
    """
    下载文件并解压到临时目录，完成后删除压缩包
    使用patool调用7zip进行解压
    """
    user_code = get_current_user_code()

    # 获取临时目录路径
    临时目录 = tempfile.gettempdir()
    下载文件路径 = os.path.join(临时目录, f"{文件名}.zip")
    解压目录 = os.path.join(临时目录, f"{文件名}")

    send_remote_log('info', '开始下载游戏文件', {
        'filename': 文件名,
        'app_id': app_id,
        'download_url_available': bool(下载链接),
        'function': '下载并解压文件'
    }, user_code)

    # 检查并删除已存在的同名文件或文件夹
    if os.path.exists(下载文件路径):
        try:
            os.remove(下载文件路径)
        except Exception as e:
            send_remote_log('warning', '清理下载文件失败', {
                'filename': 文件名,
                'error': str(e)
            }, user_code)
            return {"status": "error", "message": "清理文件失败"}

    if os.path.exists(解压目录):
        try:
            shutil.rmtree(解压目录)
        except Exception as e:
            send_remote_log('warning', '清理解压目录失败', {
                'filename': 文件名,
                'error': str(e)
            }, user_code)
            return {"status": "error", "message": "清理目录失败"}

    # 创建解压目录（如果不存在）
    os.makedirs(解压目录, exist_ok=True)

    try:
        logger.info("Processing resources...")

        # 处理URL中的特殊字符，如空格等
        # 先解析URL
        parsed_url = urllib.parse.urlparse(下载链接)
        # 对路径部分进行编码，保留'/'和':'字符
        encoded_path = urllib.parse.quote(parsed_url.path, safe='/:')
        # 重新组合URL
        encoded_url = urllib.parse.urlunparse(
            (parsed_url.scheme, parsed_url.netloc, encoded_path,
             parsed_url.params, parsed_url.query, parsed_url.fragment)
        )

        # 下载文件
        response = requests.get(encoded_url, stream=True, timeout=60)
        file_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0

        with open(下载文件路径, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)

        send_remote_log('info', '游戏文件下载完成', {
            'filename': 文件名,
            'app_id': app_id,
            'file_size': file_size,
            'downloaded_size': downloaded_size
        }, user_code)

        logger.info("Preparing files...")

        # 使用patool调用7zip解压文件
        try:
            # 创建一个临时密码文件
            密码文件路径 = os.path.join(临时目录, f"{文件名}_temp.txt")
            with open(密码文件路径, 'w') as f:
                f.write('99588啊哈yes')

            # 临时禁用所有日志输出
            import sys
            import io

            # 保存原始输出和日志配置
            original_stdout = sys.stdout
            original_stderr = sys.stderr

            # 临时禁用所有相关的日志记录器
            loggers_to_disable = ['patoolib', 'patool', 'root']
            original_levels = {}
            original_disabled = {}

            for logger_name in loggers_to_disable:
                logger_obj = logging.getLogger(logger_name)
                original_levels[logger_name] = logger_obj.level
                original_disabled[logger_name] = logger_obj.disabled
                logger_obj.setLevel(logging.CRITICAL + 1)
                logger_obj.disabled = True

            try:
                # 使用subprocess直接调用7zip，完全绕过patoolib的输出
                import subprocess

                # 构建7zip命令
                zip_exe = r"C:\Program Files\7-Zip\7z.exe"
                if not os.path.exists(zip_exe):
                    # 如果7zip不在默认位置，回退到patoolib
                    raise FileNotFoundError("7zip not found")

                # 使用7zip命令行直接解压
                cmd = [
                    zip_exe, "x", "-y",
                    f"-p99588啊哈yes",
                    f"-o{解压目录}",
                    下载文件路径
                ]

                # 完全静默执行
                result = subprocess.run(
                    cmd,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )

                if result.returncode != 0:
                    raise Exception("7zip extraction failed")

            except (FileNotFoundError, Exception):
                # 如果直接调用7zip失败，回退到patoolib
                # 重定向输出到空设备
                null_device = io.StringIO()
                sys.stdout = null_device
                sys.stderr = null_device

                try:
                    # 使用patoolib调用7zip解压文件
                    patoolib.extract_archive(
                        下载文件路径,
                        outdir=解压目录,
                        verbosity=-1,  # 完全静默
                        interactive=False,
                        password='99588啊哈yes'
                    )
                finally:
                    pass
                # 恢复原始输出
                sys.stdout = original_stdout
                sys.stderr = original_stderr

                # 恢复日志配置
                for logger_name in loggers_to_disable:
                    logger_obj = logging.getLogger(logger_name)
                    logger_obj.setLevel(original_levels[logger_name])
                    logger_obj.disabled = original_disabled[logger_name]

            # 删除临时密码文件
            if os.path.exists(密码文件路径):
                os.remove(密码文件路径)

        except Exception as e:
            send_remote_log('error', '文件解压失败', {
                'filename': 文件名,
                'app_id': app_id,
                'error': str(e),
                'failure_stage': '文件解压',
                'error_category': '解压错误'
            }, user_code)
            return {"status": "error", "message": "文件处理失败"}

        # 删除压缩文件
        os.remove(下载文件路径)

        # 处理VDF文件并生成Lua文件
        if app_id:
            logger.info("Generating configuration...")
            send_remote_log('info', '开始处理VDF文件', {
                'app_id': app_id,
                'extract_path': 解压目录
            }, user_code)

            vdf_处理结果 = 处理解压目录中的VDF文件(解压目录, app_id)

            # 移动文件到Steam目录并清理
            移动结果 = _移动文件到Steam目录并清理(解压目录, app_id, vdf_处理结果)

            if vdf_处理结果["status"] == "success":
                logger.info("Task completed")
                send_remote_log('info', '游戏入库完成', {
                    'app_id': app_id,
                    'vdf_success': True,
                    'lua_moved': 移动结果.get('lua_moved', False),
                    'manifest_moved': 移动结果.get('manifest_moved', False)
                }, user_code)
                return {
                    "status": "success",
                    "解压路径": 解压目录,
                    "vdf_处理结果": vdf_处理结果,
                    "移动结果": 移动结果
                }
            else:
                logger.info("Task completed")
                send_remote_log('warning', '游戏入库部分失败', {
                    'app_id': app_id,
                    'vdf_success': False,
                    'vdf_error': vdf_处理结果.get('message', '未知错误')
                }, user_code)
                return {
                    "status": "success",
                    "解压路径": 解压目录,
                    "vdf_处理结果": vdf_处理结果,
                    "移动结果": 移动结果,
                    "warning": "部分处理失败"
                }
        else:
            logger.info("Task completed")
            send_remote_log('info', '文件解压完成（无VDF处理）', {
                'extract_path': 解压目录
            }, user_code)
            return {"status": "success", "解压路径": 解压目录}
    except Exception as e:
        import traceback

        # 判断失败阶段
        failure_stage = '未知阶段'
        error_category = '处理失败'

        if '下载' in str(e) or 'download' in str(e).lower():
            failure_stage = '文件下载'
            error_category = '网络错误'
        elif '解压' in str(e) or 'extract' in str(e).lower():
            failure_stage = '文件解压'
            error_category = '解压错误'
        elif 'VDF' in str(e) or 'Lua' in str(e):
            failure_stage = 'VDF处理'
            error_category = 'VDF错误'
        elif '移动' in str(e) or 'move' in str(e).lower():
            failure_stage = '文件移动'
            error_category = '文件系统错误'

        send_remote_log('error', '下载解压处理异常', {
            'filename': 文件名,
            'app_id': app_id,
            'error': str(e),
            'failure_stage': failure_stage,
            'error_category': error_category,
            'traceback': traceback.format_exc()
        }, user_code)

        if os.path.exists(下载文件路径):
            try:
                os.remove(下载文件路径)
            except:
                pass
        return {"status": "error", "message": "处理失败"}


def _移动文件到Steam目录并清理(解压目录, app_id, vdf_处理结果):
    """
    移动l.lua文件到Steam的config/stplug-in目录
    移动.manifest文件到Steam的config/depotcache和depotcache目录
    最后删除整个解压目录

    Args:
        解压目录 (str): 解压后的目录路径
        app_id (str): 应用ID
        vdf_处理结果 (dict): VDF处理结果

    Returns:
        dict: 移动和清理结果
    """
    try:
        # 导入配置检测模块获取Steam路径
        from .配置检测 import _check_steam_installation

        # 获取Steam安装路径
        steam_info = _check_steam_installation()
        if not steam_info["installed"] or not steam_info["path"]:
            return {"status": "error", "message": "未检测到Steam安装路径"}

        steam_path = steam_info["path"]
        logger.info("Installing files...")

        # 定义目标目录
        stplug_in_dir = os.path.join(steam_path, "config", "stplug-in")
        config_depotcache_dir = os.path.join(steam_path, "config", "depotcache")
        depotcache_dir = os.path.join(steam_path, "depotcache")

        # 确保目标目录存在
        os.makedirs(stplug_in_dir, exist_ok=True)
        os.makedirs(config_depotcache_dir, exist_ok=True)
        os.makedirs(depotcache_dir, exist_ok=True)

        移动结果 = {
            "lua_moved": False,
            "manifest_moved": False,
            "cleanup_done": False,
            "details": []
        }

        # 1. 移动l.lua文件到config/stplug-in目录
        lua_文件名 = f"{app_id}.lua"
        if vdf_处理结果.get("status") == "success" and "lua_文件路径" in vdf_处理结果:
            源lua路径 = vdf_处理结果["lua_文件路径"]
            目标lua路径 = os.path.join(stplug_in_dir, lua_文件名)

            # 如果目标文件存在，先删除
            if os.path.exists(目标lua路径):
                os.remove(目标lua路径)
                移动结果["details"].append(f"删除已存在的{lua_文件名}")

            # 移动文件
            if os.path.exists(源lua路径):
                # 清洗Lua文件内容
                try:
                    with open(源lua路径, 'r', encoding='utf-8') as f:
                        原始内容 = f.readlines()

                    清洗后内容 = []

                    for line in 原始内容:
                        # 移除空行
                        if line.strip() == '':
                            continue

                        # 移除包含 setManifestid 的行
                        if 'setManifestid' in line:
                            continue

                        # 如果行包含 -- 注释
                        if '--' in line:
                            # 检查是否是函数调用行（包含addappid等）
                            if 'addappid(' in line:
                                # 保留函数调用部分，移除注释部分
                                code_part = line.split('--')[0].strip()
                                if code_part:  # 确保不是空行
                                    清洗后内容.append(code_part + '\n')
                            # 如果是纯注释行，直接跳过
                            continue
                        else:
                            # 没有注释的行直接保留
                            清洗后内容.append(line)

                    with open(源lua路径, 'w', encoding='utf-8') as f:
                        f.writelines(清洗后内容)

                except Exception as e:
                    # 处理清洗异常，不影响主流程
                    pass

                # 移动文件
                shutil.move(源lua路径, 目标lua路径)
                移动结果["lua_moved"] = True
                移动结果["details"].append(f"成功移动{lua_文件名}到stplug-in目录")

        # 2. 查找并移动.manifest文件
        manifest_files = []
        for root, dirs, files in os.walk(解压目录):
            for file in files:
                if file.endswith('.manifest'):
                    manifest_files.append(os.path.join(root, file))

        if manifest_files:
            for manifest_file in manifest_files:
                manifest_filename = os.path.basename(manifest_file)

                # 移动到config/depotcache目录
                目标config_manifest = os.path.join(config_depotcache_dir, manifest_filename)
                if os.path.exists(目标config_manifest):
                    os.remove(目标config_manifest)
                shutil.copy2(manifest_file, 目标config_manifest)

                # 移动到depotcache目录
                目标depot_manifest = os.path.join(depotcache_dir, manifest_filename)
                if os.path.exists(目标depot_manifest):
                    os.remove(目标depot_manifest)
                shutil.copy2(manifest_file, 目标depot_manifest)

                移动结果["details"].append(f"成功移动{manifest_filename}到depotcache目录")

            移动结果["manifest_moved"] = True

        # 3. 删除整个解压目录
        if os.path.exists(解压目录):
            shutil.rmtree(解压目录)
            移动结果["cleanup_done"] = True
            移动结果["details"].append("成功清理解压目录")

        logger.info("Installation completed")
        return {
            "status": "success",
            "message": "文件移动和清理完成",
            "结果": 移动结果
        }

    except Exception as e:
        import traceback
        user_code = get_current_user_code()
        send_remote_log('error', '文件移动和清理失败', {
            'app_id': app_id,
            'extract_path': 解压目录,
            'error': str(e),
            'failure_stage': '文件移动',
            'error_category': '文件系统错误',
            'traceback': traceback.format_exc()
        }, user_code)
        logger.error(f"文件移动和清理失败: {str(e)}")
        return {
            "status": "error",
            "message": f"文件移动和清理失败: {str(e)}"
        }